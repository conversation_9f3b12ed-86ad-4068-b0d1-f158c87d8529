@echo off
setlocal enabledelayedexpansion

echo.
echo 🚀 Creating Inno-CRM Admin Server...
echo.

REM Check prerequisites
echo 🔍 Checking prerequisites...

where git >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Git is not installed or not in PATH
    pause
    exit /b 1
)

where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    pause
    exit /b 1
)

where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm is not installed or not in PATH
    pause
    exit /b 1
)

echo ✅ All prerequisites met
echo.

REM Get directories
set "currentDir=%cd%"
for %%i in ("%currentDir%") do set "parentDir=%%~dpi"
set "adminDir=%parentDir%inno-crm-admin"

echo 📁 Directory Information:
echo    Current (Staff): %currentDir%
echo    Parent: %parentDir%
echo    Admin (New): %adminDir%
echo.

REM Check if admin directory exists
if exist "%adminDir%" (
    echo ⚠️  Admin directory already exists: %adminDir%
    set /p "response=Do you want to remove it and recreate? (y/N): "
    if /i "!response!"=="y" (
        echo 🗑️  Removing existing admin directory...
        rmdir /s /q "%adminDir%"
    ) else (
        echo ❌ Aborted by user
        pause
        exit /b 1
    )
)

REM Step 1: Clone repository
echo 📋 Step 1: Cloning current repository for admin server...

REM Get remote URL
for /f "tokens=*" %%i in ('git remote get-url origin') do set "remoteUrl=%%i"
echo    Cloning from: !remoteUrl!

REM Change to parent directory and clone
cd /d "%parentDir%"
git clone "!remoteUrl!" "inno-crm-admin"

if not exist "%adminDir%" (
    echo ❌ Failed to clone repository
    pause
    exit /b 1
)

echo ✅ Repository cloned successfully
echo.

REM Step 2: Setup admin repository
echo 📋 Step 2: Setting up admin repository...

cd /d "%adminDir%"

REM Remove git history
if exist ".git" (
    rmdir /s /q ".git"
    echo    ✅ Removed existing git history
)

REM Initialize new git repository
git init
echo    ✅ Initialized new git repository

REM Add remote
git remote add origin "https://github.com/MrFarrukhT/inno-crm-admin.git"
echo    ✅ Added remote origin for admin repository
echo.

REM Step 3: Install dependencies
echo 📋 Step 3: Installing dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)
echo ✅ Dependencies installed successfully
echo.

REM Step 4: Run setup script
echo 📋 Step 4: Running admin setup script...
if exist "scripts\setup-admin-server.js" (
    node scripts\setup-admin-server.js
    echo ✅ Admin setup script completed
) else (
    echo ⚠️  Admin setup script not found, continuing...
)
echo.

REM Step 5: Manual configuration
echo 📋 Step 5: Updating configuration files...

REM Copy admin files if they exist
if exist "vercel.admin.json" (
    copy "vercel.admin.json" "vercel.json" >nul
    echo    ✅ Updated vercel.json
)

if exist ".env.production.admin" (
    copy ".env.production.admin" ".env.production" >nul
    echo    ✅ Updated .env.production
)

echo    ✅ Configuration files updated
echo.

REM Step 6: Database setup reminder
echo 📋 Step 6: Database Setup Required
echo.
echo 🔐 You need to create an admin database:
echo    Option A: Create new Neon database project 'inno-crm-admin'
echo    Option B: Create new database in existing Neon project
echo.
echo    Then update DATABASE_URL in .env.production with the new connection string
echo.

REM Step 7: Commit changes
echo 📋 Step 7: Committing changes...
git add .
git commit -m "Initial admin server setup"
if %errorlevel% neq 0 (
    echo ❌ Failed to commit changes
    echo    You can commit manually later
) else (
    echo ✅ Changes committed successfully
)
echo.

REM Final instructions
echo 🎯 Admin Server Created Successfully!
echo.
echo 📍 Admin server location: %adminDir%
echo.
echo 📋 Next Steps:
echo 1. 🔐 Create admin database (see instructions above)
echo 2. 📝 Update DATABASE_URL in .env.production
echo 3. 🌐 Create GitHub repository: https://github.com/new
echo    - Repository name: inno-crm-admin
echo    - Description: Innovative Centre CRM System - Admin Server
echo    - Visibility: Private
echo 4. 📤 Push to GitHub:
echo    git push -u origin main
echo 5. 🚀 Deploy to Vercel: https://vercel.com/new
echo    - Import inno-crm-admin repository
echo    - Project name: inno-crm-admin
echo    - Configure environment variables
echo.
echo 📖 Documentation:
echo    - Full guide: CREATE_ADMIN_SERVER_GUIDE.md
echo    - Environment variables: ENVIRONMENT_VARIABLES_GUIDE.md
echo    - Deployment checklist: DEPLOYMENT_CHECKLIST.md
echo.

REM Return to original directory
cd /d "%currentDir%"

echo ✅ Admin server creation completed!
echo 📁 You can now navigate to: %adminDir%
echo.
pause
