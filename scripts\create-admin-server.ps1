# PowerShell Script to Create Admin Server
# This script automates the creation of the admin server repository

Write-Host "🚀 Creating Inno-CRM Admin Server..." -ForegroundColor Green
Write-Host ""

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

if (-not (Test-Command "git")) {
    Write-Host "❌ Git is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

if (-not (Test-Command "node")) {
    Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

if (-not (Test-Command "npm")) {
    Write-Host "❌ npm is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

Write-Host "✅ All prerequisites met" -ForegroundColor Green
Write-Host ""

# Get current directory and parent directory
$currentDir = Get-Location
$parentDir = Split-Path $currentDir -Parent
$adminDir = Join-Path $parentDir "inno-crm-admin"

Write-Host "📁 Directory Information:" -ForegroundColor Cyan
Write-Host "   Current (Staff): $currentDir"
Write-Host "   Parent: $parentDir"
Write-Host "   Admin (New): $adminDir"
Write-Host ""

# Check if admin directory already exists
if (Test-Path $adminDir) {
    Write-Host "⚠️  Admin directory already exists: $adminDir" -ForegroundColor Yellow
    $response = Read-Host "Do you want to remove it and recreate? (y/N)"
    if ($response -eq "y" -or $response -eq "Y") {
        Write-Host "🗑️  Removing existing admin directory..." -ForegroundColor Yellow
        Remove-Item -Recurse -Force $adminDir
    } else {
        Write-Host "❌ Aborted by user" -ForegroundColor Red
        exit 1
    }
}

# Step 1: Clone current repository
Write-Host "📋 Step 1: Cloning current repository for admin server..." -ForegroundColor Yellow

try {
    # Get the remote URL of current repository
    $remoteUrl = git remote get-url origin
    Write-Host "   Cloning from: $remoteUrl"
    
    # Clone to admin directory
    Set-Location $parentDir
    git clone $remoteUrl "inno-crm-admin"
    
    if (-not (Test-Path $adminDir)) {
        throw "Failed to clone repository"
    }
    
    Write-Host "✅ Repository cloned successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to clone repository: $_" -ForegroundColor Red
    exit 1
}

# Step 2: Navigate to admin directory and setup
Write-Host ""
Write-Host "📋 Step 2: Setting up admin repository..." -ForegroundColor Yellow

try {
    Set-Location $adminDir
    
    # Remove existing git history
    if (Test-Path ".git") {
        Remove-Item -Recurse -Force ".git"
        Write-Host "   ✅ Removed existing git history"
    }
    
    # Initialize new git repository
    git init
    Write-Host "   ✅ Initialized new git repository"
    
    # Add remote for admin repository (user will need to create this on GitHub)
    git remote add origin "https://github.com/MrFarrukhT/inno-crm-admin.git"
    Write-Host "   ✅ Added remote origin for admin repository"
    
} catch {
    Write-Host "❌ Failed to setup git repository: $_" -ForegroundColor Red
    exit 1
}

# Step 3: Install dependencies
Write-Host ""
Write-Host "📋 Step 3: Installing dependencies..." -ForegroundColor Yellow

try {
    npm install
    Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install dependencies: $_" -ForegroundColor Red
    exit 1
}

# Step 4: Run admin setup script
Write-Host ""
Write-Host "📋 Step 4: Running admin setup script..." -ForegroundColor Yellow

try {
    if (Test-Path "scripts/setup-admin-server.js") {
        node scripts/setup-admin-server.js
        Write-Host "✅ Admin setup script completed" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Admin setup script not found, continuing..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Failed to run admin setup script: $_" -ForegroundColor Red
    Write-Host "   Continuing with manual configuration..." -ForegroundColor Yellow
}

# Step 5: Manual configuration updates
Write-Host ""
Write-Host "📋 Step 5: Updating configuration files..." -ForegroundColor Yellow

try {
    # Update package.json
    if (Test-Path "package.json") {
        $packageJson = Get-Content "package.json" -Raw | ConvertFrom-Json
        $packageJson.name = "inno-crm-admin"
        $packageJson.scripts.dev = "next dev"
        $packageJson | ConvertTo-Json -Depth 10 | Set-Content "package.json"
        Write-Host "   ✅ Updated package.json"
    }
    
    # Copy admin vercel configuration
    if (Test-Path "vercel.admin.json") {
        Copy-Item "vercel.admin.json" "vercel.json" -Force
        Write-Host "   ✅ Updated vercel.json"
    }
    
    # Copy admin environment configuration
    if (Test-Path ".env.production.admin") {
        Copy-Item ".env.production.admin" ".env.production" -Force
        Write-Host "   ✅ Updated .env.production"
    }
    
    # Update README.md
    if (Test-Path "README.md") {
        $readme = Get-Content "README.md" -Raw
        $readme = $readme -replace "Staff Server", "Admin Server"
        $readme = $readme -replace "staff server", "admin server"
        $readme = $readme -replace "reception and teaching staff with limited functionality", "administrators and cashiers with full system access"
        $readme = $readme -replace "port 3001", "port 3000"
        $readme | Set-Content "README.md"
        Write-Host "   ✅ Updated README.md"
    }
    
} catch {
    Write-Host "❌ Failed to update configuration files: $_" -ForegroundColor Red
    Write-Host "   You may need to update them manually" -ForegroundColor Yellow
}

# Step 6: Database setup reminder
Write-Host ""
Write-Host "📋 Step 6: Database Setup Required" -ForegroundColor Yellow
Write-Host ""
Write-Host "🔐 You need to create an admin database:" -ForegroundColor Cyan
Write-Host "   Option A: Create new Neon database project 'inno-crm-admin'"
Write-Host "   Option B: Create new database in existing Neon project"
Write-Host ""
Write-Host "   Then update DATABASE_URL in .env.production with the new connection string"
Write-Host ""

# Step 7: Commit changes
Write-Host "📋 Step 7: Committing changes..." -ForegroundColor Yellow

try {
    git add .
    git commit -m "Initial admin server setup"
    Write-Host "✅ Changes committed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to commit changes: $_" -ForegroundColor Red
    Write-Host "   You can commit manually later" -ForegroundColor Yellow
}

# Step 8: Instructions for next steps
Write-Host ""
Write-Host "🎯 Admin Server Created Successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Admin server location: $adminDir" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. 🔐 Create admin database (see instructions above)"
Write-Host "2. 📝 Update DATABASE_URL in .env.production"
Write-Host "3. 🌐 Create GitHub repository: https://github.com/new"
Write-Host "   - Repository name: inno-crm-admin"
Write-Host "   - Description: Innovative Centre CRM System - Admin Server"
Write-Host "   - Visibility: Private"
Write-Host "4. 📤 Push to GitHub:"
Write-Host "   git push -u origin main"
Write-Host "5. 🚀 Deploy to Vercel: https://vercel.com/new"
Write-Host "   - Import inno-crm-admin repository"
Write-Host "   - Project name: inno-crm-admin"
Write-Host "   - Configure environment variables"
Write-Host ""
Write-Host "📖 Documentation:" -ForegroundColor Cyan
Write-Host "   - Full guide: CREATE_ADMIN_SERVER_GUIDE.md"
Write-Host "   - Environment variables: ENVIRONMENT_VARIABLES_GUIDE.md"
Write-Host "   - Deployment checklist: DEPLOYMENT_CHECKLIST.md"
Write-Host ""

# Return to original directory
Set-Location $currentDir

Write-Host "✅ Admin server creation completed!" -ForegroundColor Green
Write-Host "📁 You can now navigate to: $adminDir" -ForegroundColor Cyan
