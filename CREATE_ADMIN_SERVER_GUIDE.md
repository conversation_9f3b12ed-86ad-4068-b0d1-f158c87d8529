# Create Admin Server - Step by Step Guide

This guide will help you create the admin server repository from your current staff server.

## 🎯 Overview

We'll create a separate `inno-crm-admin` repository that has full functionality for administrators and cashiers.

## 📋 Prerequisites

- Current staff server repository (your current folder)
- Git installed
- GitHub account
- Admin database (we'll help you create this)

## 🚀 Step-by-Step Instructions

### Step 1: Navigate to Your Codes Directory

```bash
# Open PowerShell or Command Prompt
# Navigate to your codes directory (parent of current repository)
cd "C:/Users/<USER>/Desktop/codes"
```

### Step 2: Clone Current Repository for Admin Version

```bash
# Clone your current repository to create admin version
git clone https://github.com/MrFarrukhT/inno-crm.git inno-crm-admin

# Navigate to the new admin folder
cd inno-crm-admin
```

### Step 3: Remove Git History and Prepare for New Repository

```bash
# Remove existing git history
Remove-Item -Recurse -Force .git

# Initialize new git repository
git init

# Add remote for new admin repository
git remote add origin https://github.com/MrFarrukhT/inno-crm-admin.git
```

### Step 4: Run Admin Setup Script

```bash
# Install dependencies
npm install

# Run the admin setup script
node scripts/setup-admin-server.js
```

This script will:
- Update package.json for admin server
- Copy admin Vercel configuration
- Copy admin environment configuration
- Update README.md for admin server
- Create admin-specific middleware configuration

### Step 5: Create Admin Database

#### Option A: Create New Neon Database (Recommended)

1. **Go to Neon Console**: [console.neon.tech](https://console.neon.tech/)
2. **Create New Project**: 
   - Project Name: `inno-crm-admin`
   - Region: Same as your staff database
3. **Get Connection String**: Copy the connection string
4. **Update Environment**: Replace DATABASE_URL in `.env.production`

#### Option B: Use Existing Neon Project with New Database

1. **Go to Your Existing Neon Project**
2. **Create New Database**: 
   - Database Name: `crm-admin`
3. **Update Connection String**: Change database name in URL
4. **Update Environment**: Replace DATABASE_URL in `.env.production`

### Step 6: Configure Environment Variables

Update `.env.production` with admin-specific values:

```env
# Database Configuration - Admin Database
DATABASE_URL="your-new-admin-database-url"

# NextAuth.js Configuration
NEXTAUTH_SECRET="inno-crm-admin-super-secret-key-for-production-2024-very-long-and-secure"
NEXTAUTH_URL="https://inno-crm-admin.vercel.app"

# Application Configuration
APP_NAME="Innovative Centre - Admin Portal"
APP_URL="https://inno-crm-admin.vercel.app"
SERVER_TYPE="admin"

# Feature Flags - Admin Server (All Features Enabled)
FEATURE_ANALYTICS_ENABLED="true"
FEATURE_REPORTS_ENABLED="true"
FEATURE_ADMIN_PANEL="true"
FEATURE_FINANCIAL_REPORTS="true"
FEATURE_KPI_TRACKING="true"

# Admin-specific Security
ADMIN_IP_WHITELIST="your.admin.ip.address"
```

### Step 7: Initialize Database

```bash
# Generate Prisma client
npx prisma generate

# Push database schema
npx prisma db push

# Seed database with initial data
npx prisma db seed
```

### Step 8: Test Build

```bash
# Test the build process
npm run build
```

### Step 9: Commit and Push to GitHub

```bash
# Stage all changes
git add .

# Commit changes
git commit -m "Initial admin server setup"

# Push to new repository (you'll need to create this on GitHub first)
git push -u origin main
```

### Step 10: Create GitHub Repository

1. **Go to GitHub**: [github.com/new](https://github.com/new)
2. **Repository Name**: `inno-crm-admin`
3. **Description**: "Innovative Centre CRM System - Admin Server"
4. **Visibility**: Private (recommended)
5. **Create Repository**: Don't initialize with README (we already have one)

### Step 11: Deploy to Vercel

1. **Go to Vercel**: [vercel.com/new](https://vercel.com/new)
2. **Import Repository**: Select `inno-crm-admin`
3. **Project Settings**:
   - Project Name: `inno-crm-admin`
   - Framework: Next.js (auto-detected)
4. **Environment Variables**: Copy from `.env.production`
5. **Deploy**: Click deploy

## 🔧 Key Differences: Admin vs Staff Server

### Admin Server Features (All Enabled)
- ✅ Full analytics dashboard
- ✅ Payment management
- ✅ Financial reports
- ✅ KPI tracking
- ✅ Admin user management
- ✅ System administration
- ✅ All staff server features

### Staff Server Features (Limited)
- ✅ Lead management
- ✅ Student management
- ✅ Course/group management
- ✅ Attendance tracking
- ✅ Assessment management
- ❌ Financial analytics
- ❌ Payment management
- ❌ Admin functions

## 🧪 Testing Admin Server

### 1. Health Check
```bash
curl https://inno-crm-admin.vercel.app/api/health
```

### 2. Inter-Server Communication
```bash
curl -X GET https://inno-crm-admin.vercel.app/api/inter-server/health \
  -H "X-Inter-Server-Secret: your-secret" \
  -H "User-Agent: staff-server"
```

### 3. Login Testing
- Test with admin user credentials
- Verify all features are accessible
- Check analytics dashboard
- Test payment management

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Error
- Verify DATABASE_URL is correct
- Check database accessibility
- Ensure Prisma schema is applied

#### Build Failures
- Run `npm install` to ensure dependencies
- Run `npx prisma generate` to generate client
- Check for TypeScript errors

#### GitHub Push Issues
- Ensure repository exists on GitHub
- Check remote URL is correct
- Verify authentication

## ✅ Success Checklist

- [ ] Admin repository created and cloned
- [ ] Admin setup script executed successfully
- [ ] Admin database created and configured
- [ ] Environment variables updated
- [ ] Database schema applied
- [ ] Build process successful
- [ ] Repository pushed to GitHub
- [ ] Deployed to Vercel
- [ ] Health checks passing
- [ ] Admin features accessible

## 🎉 Completion

When all steps are complete:
1. **Staff Server**: `https://inno-crm-staff.vercel.app`
2. **Admin Server**: `https://inno-crm-admin.vercel.app`
3. **Inter-server communication**: Working between both servers
4. **Role-based access**: Properly enforced

Your dual-server CRM system is ready for production! 🚀
