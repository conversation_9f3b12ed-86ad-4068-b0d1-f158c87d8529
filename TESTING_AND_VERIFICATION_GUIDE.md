# Testing and Verification Guide

This guide provides comprehensive testing procedures for both Admin and Staff servers after deployment.

## 🎯 Testing Overview

### Testing Phases
1. **Individual Server Testing** - Test each server independently
2. **Inter-Server Communication Testing** - Test communication between servers
3. **End-to-End Testing** - Test complete user workflows
4. **Performance Testing** - Verify performance and reliability

## 🏢 Staff Server Testing

### Basic Functionality Tests

#### 1. Health Check
```bash
# Test basic health endpoint
curl https://inno-crm-staff.vercel.app/api/health

# Expected response:
{
  "status": "healthy",
  "timestamp": "2024-06-27T...",
  "database": "connected",
  "server": "staff"
}
```

#### 2. Application Access
- **URL**: `https://inno-crm-staff.vercel.app`
- **Expected**: Login page loads correctly
- **Check**: No console errors, proper styling

#### 3. Authentication Testing
Test with these user types:
- **Reception User**: Should have access
- **Teacher User**: Should have access
- **Manager User**: Should have access
- **Academic Manager**: Should have access
- **Student User**: Should have access
- **Admin User**: Should be blocked (if exists)
- **Cashier User**: Should be blocked (if exists)

#### 4. Feature Access Verification
Test that these features are **DISABLED** on staff server:
- [ ] Analytics dashboard (`/dashboard/analytics`)
- [ ] Financial reports (`/dashboard/reports`)
- [ ] Payment management (`/dashboard/payments`)
- [ ] Admin panel (`/dashboard/admin`)
- [ ] KPI tracking

Test that these features are **ENABLED** on staff server:
- [ ] Lead management (`/dashboard/leads`)
- [ ] Student management (`/dashboard/students`)
- [ ] Course management (`/dashboard/courses`)
- [ ] Group management (`/dashboard/groups`)
- [ ] Attendance tracking (`/dashboard/attendance`)
- [ ] Assessment management (`/dashboard/assessments`)

#### 5. Database Connectivity
```bash
# Test database operations through API
curl -X GET https://inno-crm-staff.vercel.app/api/students \
  -H "Authorization: Bearer YOUR_TOKEN"

# Should return student data or proper authentication error
```

### Inter-Server Communication Tests

#### 1. Inter-Server Health Check
```bash
# Test inter-server health endpoint
curl -X GET https://inno-crm-staff.vercel.app/api/inter-server/health \
  -H "X-Inter-Server-Secret: your-inter-server-secret" \
  -H "User-Agent: admin-server"

# Expected response:
{
  "status": "healthy",
  "server": "staff",
  "timestamp": "2024-06-27T...",
  "version": "1.0.0",
  "environment": "production"
}
```

#### 2. Authentication Validation (when admin server is ready)
```bash
# Test user authentication validation
curl -X POST https://inno-crm-admin.vercel.app/api/inter-server/auth/validate \
  -H "Content-Type: application/json" \
  -H "X-Inter-Server-Secret: your-inter-server-secret" \
  -H "User-Agent: staff-server" \
  -d '{"phone":"+998901234567","password":"testpassword"}'
```

## 🔐 Admin Server Testing (After Setup)

### Basic Functionality Tests

#### 1. Health Check
```bash
curl https://inno-crm-admin.vercel.app/api/health
```

#### 2. Application Access
- **URL**: `https://inno-crm-admin.vercel.app`
- **Expected**: Login page with admin branding

#### 3. Authentication Testing
Test with these user types:
- **Admin User**: Should have full access
- **Cashier User**: Should have full access
- **All Staff Roles**: Should have access (admin server allows all)

#### 4. Feature Access Verification
Test that ALL features are **ENABLED** on admin server:
- [ ] Analytics dashboard (`/dashboard/analytics`)
- [ ] Financial reports (`/dashboard/reports`)
- [ ] Payment management (`/dashboard/payments`)
- [ ] Admin panel (`/dashboard/admin`)
- [ ] KPI tracking (`/dashboard/kpis`)
- [ ] All staff server features

#### 5. Admin-Specific Features
- [ ] User management with all roles
- [ ] Financial analytics
- [ ] Payment processing
- [ ] System administration
- [ ] Audit logs

## 🔄 Inter-Server Communication Testing

### 1. Bidirectional Health Checks
```bash
# Staff to Admin
curl -X GET https://inno-crm-admin.vercel.app/api/inter-server/health \
  -H "X-Inter-Server-Secret: your-secret" \
  -H "User-Agent: staff-server"

# Admin to Staff
curl -X GET https://inno-crm-staff.vercel.app/api/inter-server/health \
  -H "X-Inter-Server-Secret: your-secret" \
  -H "User-Agent: admin-server"
```

### 2. User Authentication Flow
1. User logs in to staff server
2. Staff server validates credentials with admin server
3. Admin server returns user data
4. Staff server creates session

### 3. Data Synchronization
```bash
# Test data sync from staff to admin
curl -X POST https://inno-crm-admin.vercel.app/api/inter-server/sync \
  -H "Content-Type: application/json" \
  -H "X-Inter-Server-Secret: your-secret" \
  -H "User-Agent: staff-server" \
  -d '{
    "type": "student",
    "operation": "sync",
    "data": {
      "id": "test-123",
      "name": "Test Student",
      "phone": "+998901234567"
    }
  }'
```

## 🧪 End-to-End Testing Scenarios

### Scenario 1: Reception User Workflow
1. **Login**: Reception user logs in to staff server
2. **Lead Management**: Create and manage leads
3. **Student Enrollment**: Convert lead to student
4. **Course Assignment**: Assign student to course/group
5. **Attendance**: Mark attendance for classes

### Scenario 2: Teacher User Workflow
1. **Login**: Teacher logs in to staff server
2. **Class Management**: View assigned classes
3. **Attendance**: Mark student attendance
4. **Assessments**: Create and grade assessments
5. **Progress Tracking**: Update student progress

### Scenario 3: Admin User Workflow
1. **Login**: Admin logs in to admin server
2. **Analytics**: View comprehensive analytics
3. **Financial Reports**: Generate financial reports
4. **User Management**: Manage all user types
5. **System Administration**: Configure system settings

### Scenario 4: Cross-Server Data Flow
1. **Staff Action**: Reception creates student on staff server
2. **Data Sync**: Student data syncs to admin server
3. **Admin Analysis**: Admin views student in analytics
4. **Financial Tracking**: Payment data tracked on admin server

## 📊 Performance Testing

### Load Testing
```bash
# Test concurrent requests
for i in {1..10}; do
  curl -s https://inno-crm-staff.vercel.app/api/health &
done
wait
```

### Response Time Testing
```bash
# Measure response times
time curl https://inno-crm-staff.vercel.app/api/health
time curl https://inno-crm-admin.vercel.app/api/health
```

### Database Performance
- Test with multiple concurrent database operations
- Monitor query execution times
- Check connection pool usage

## 🔍 Security Testing

### Authentication Security
- [ ] Invalid credentials rejected
- [ ] Session management working
- [ ] Role-based access enforced
- [ ] Inter-server authentication required

### CORS Testing
```bash
# Test CORS from different origins
curl -X OPTIONS https://inno-crm-staff.vercel.app/api/inter-server/health \
  -H "Origin: https://inno-crm-admin.vercel.app" \
  -H "Access-Control-Request-Method: GET"
```

### Input Validation
- Test with malformed requests
- Verify SQL injection protection
- Check XSS prevention

## 📋 Testing Checklist

### Staff Server Checklist
- [ ] Health endpoint responds correctly
- [ ] Application loads without errors
- [ ] Staff users can log in
- [ ] Admin/Cashier users are blocked
- [ ] Limited features accessible
- [ ] Database operations working
- [ ] Inter-server endpoints respond
- [ ] CORS configured correctly

### Admin Server Checklist
- [ ] Health endpoint responds correctly
- [ ] Application loads without errors
- [ ] Admin/Cashier users can log in
- [ ] All users can access (when appropriate)
- [ ] All features accessible
- [ ] Analytics dashboard working
- [ ] Payment management working
- [ ] Inter-server endpoints respond

### Integration Checklist
- [ ] Staff to admin communication working
- [ ] Admin to staff communication working
- [ ] User authentication across servers
- [ ] Data synchronization functional
- [ ] Role-based access enforced
- [ ] Security measures active

## 🚨 Troubleshooting Common Issues

### Build/Deployment Issues
- Check Vercel build logs
- Verify environment variables
- Ensure Prisma generation succeeds

### Authentication Issues
- Verify NEXTAUTH_URL matches deployment URL
- Check NEXTAUTH_SECRET is set correctly
- Ensure database connectivity

### Inter-Server Issues
- Verify INTER_SERVER_SECRET matches
- Check CORS configuration
- Ensure server URLs are correct
- Test network connectivity

### Database Issues
- Verify DATABASE_URL format
- Check database accessibility
- Ensure Prisma schema is up to date

## ✅ Success Criteria

### Individual Server Success
- All health checks pass
- Authentication works correctly
- Features accessible based on server type
- Database operations functional
- No console errors

### Integration Success
- Inter-server communication functional
- User authentication works across servers
- Data synchronization working
- Role-based access properly enforced
- Security measures active

### Performance Success
- Response times under 2 seconds
- No memory leaks
- Database queries optimized
- Concurrent requests handled properly

## 📝 Test Results Documentation

Create a test results document with:
- Test execution date/time
- Test environment details
- Pass/fail status for each test
- Performance metrics
- Issues found and resolutions
- Recommendations for improvements

## 🎉 Testing Complete

When all tests pass:
1. Both servers are fully functional
2. Inter-server communication is working
3. Security measures are effective
4. Performance is acceptable
5. User workflows are complete

**Your Inno-CRM dual-server deployment is ready for production use!** 🚀
